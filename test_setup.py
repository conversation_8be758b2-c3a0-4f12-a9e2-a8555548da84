#!/usr/bin/env python3
"""
Test script to validate the PDF extractor setup
Run this script to check if all dependencies are installed correctly
"""

import sys
import importlib
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    required_modules = [
        'google.cloud.storage',
        'PyPDF2',
        'pdfplumber',
        'pandas',
        'openpyxl',
        'fitz',  # PyMuPDF
    ]
    
    print("Testing module imports...")
    failed_imports = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✓ {module}")
        except ImportError as e:
            print(f"✗ {module} - {e}")
            failed_imports.append(module)
    
    return failed_imports

def test_config():
    """Test if configuration file is accessible."""
    print("\nTesting configuration...")
    try:
        import config
        print(f"✓ Configuration loaded")
        print(f"  - Bucket: {config.GCS_BUCKET_NAME}")
        print(f"  - Folder: {config.GCS_FOLDER_PATH}")
        print(f"  - Output: {config.OUTPUT_DIRECTORY}")
        return True
    except ImportError as e:
        print(f"✗ Configuration failed - {e}")
        return False

def test_gcs_auth():
    """Test Google Cloud Storage authentication."""
    print("\nTesting Google Cloud authentication...")
    try:
        from google.cloud import storage
        client = storage.Client()
        # Try to list buckets (this will fail if auth is not set up)
        list(client.list_buckets(max_results=1))
        print("✓ Google Cloud authentication working")
        return True
    except Exception as e:
        print(f"✗ Google Cloud authentication failed - {e}")
        print("  Run: gcloud auth application-default login")
        return False

def test_output_directory():
    """Test if output directory can be created."""
    print("\nTesting output directory...")
    try:
        import config
        os.makedirs(config.OUTPUT_DIRECTORY, exist_ok=True)
        if os.path.exists(config.OUTPUT_DIRECTORY):
            print(f"✓ Output directory ready: {config.OUTPUT_DIRECTORY}")
            return True
        else:
            print(f"✗ Could not create output directory: {config.OUTPUT_DIRECTORY}")
            return False
    except Exception as e:
        print(f"✗ Output directory test failed - {e}")
        return False

def test_pdf_extractor():
    """Test if the main PDF extractor can be imported."""
    print("\nTesting PDF extractor...")
    try:
        from pdf_extractor import PDFDataExtractor
        extractor = PDFDataExtractor()
        print("✓ PDF extractor initialized successfully")
        return True
    except Exception as e:
        print(f"✗ PDF extractor test failed - {e}")
        return False

def main():
    """Run all tests."""
    print("PDF Extractor Setup Test")
    print("=" * 40)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_config),
        ("GCS Authentication", test_gcs_auth),
        ("Output Directory", test_output_directory),
        ("PDF Extractor", test_pdf_extractor),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"✗ {test_name} - Unexpected error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "PASS" if passed else "FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! You're ready to run the PDF extractor.")
        print("\nTo run the extractor:")
        print("  python3 pdf_extractor.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        print("\nCommon fixes:")
        print("  - Run: pip3 install -r requirements.txt")
        print("  - Run: gcloud auth application-default login")
        print("  - Check your Google Cloud permissions")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
