{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PDF Data Extractor for Adidas Documents\n", "\n", "This notebook extracts specific information from PDF documents stored in Google Cloud Storage:\n", "- **Seasonal sourcing sizes**\n", "- **Fiber Text Position**\n", "- **Care symbols**\n", "- **Care Instructions**\n", "\n", "Each PDF will generate a separate Excel file with the extracted data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Install Required Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install google-cloud-storage PyPDF2 pdfplumber PyMuPDF pandas openpyxl --quiet\n", "\n", "print(\"✅ Dependencies installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Settings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration settings - Modify these as needed\n", "CONFIG = {\n", "    # Google Cloud Storage settings\n", "    'GCS_BUCKET_NAME': 'dice_gstorage',\n", "    'GCS_FOLDER_PATH': 'adidas/',\n", "    \n", "    # Output settings\n", "    'OUTPUT_DIRECTORY': 'output_excel_files',\n", "    'EXCEL_FILE_SUFFIX': '_extracted_data.xlsx',\n", "    \n", "    # Extraction patterns for different data types\n", "    'EXTRACTION_PATTERNS': {\n", "        'seasonal_sizes': [\n", "            r'seasonal\\s+sourcing\\s+sizes?[:\\s]*([^\\n]+)',\n", "            r'sizes?\\s*[:\\-]\\s*([XS|S|M|L|XL|XXL|\\d+\\-\\d+|\\d+\\/\\d+][,\\s]*)+',\n", "            r'size\\s+range[:\\s]*([^\\n]+)',\n", "            r'available\\s+sizes?[:\\s]*([^\\n]+)'\n", "        ],\n", "        'fiber_position': [\n", "            r'fiber\\s+text\\s+position[:\\s]*([^\\n]+)',\n", "            r'fiber\\s+position[:\\s]*([^\\n]+)',\n", "            r'text\\s+position[:\\s]*([^\\n]+)',\n", "            r'label\\s+position[:\\s]*([^\\n]+)'\n", "        ],\n", "        'care_symbols': [\n", "            r'care\\s+symbols?[:\\s]*([^\\n]+)',\n", "            r'washing\\s+symbols?[:\\s]*([^\\n]+)',\n", "            r'laundry\\s+symbols?[:\\s]*([^\\n]+)',\n", "            r'maintenance\\s+symbols?[:\\s]*([^\\n]+)'\n", "        ],\n", "        'care_instructions': [\n", "            r'care\\s+instructions?[:\\s]*([^\\n\\r]+(?:\\n[^\\n\\r]+)*)',\n", "            r'washing\\s+instructions?[:\\s]*([^\\n\\r]+(?:\\n[^\\n\\r]+)*)',\n", "            r'laundry\\s+instructions?[:\\s]*([^\\n\\r]+(?:\\n[^\\n\\r]+)*)',\n", "            r'maintenance\\s+instructions?[:\\s]*([^\\n\\r]+(?:\\n[^\\n\\r]+)*)'\n", "        ]\n", "    },\n", "    \n", "    # Excel styling\n", "    'EXCEL_STYLING': {\n", "        'header_font_color': 'FFFFFF',\n", "        'header_background_color': '366092',\n", "        'column_width_category': 25,\n", "        'column_width_data': 50,\n", "        'max_text_length': 200\n", "    }\n", "}\n", "\n", "print(\"✅ Configuration loaded successfully!\")\n", "print(f\"📁 Bucket: {CONFIG['GCS_BUCKET_NAME']}\")\n", "print(f\"📂 Folder: {CONFIG['GCS_FOLDER_PATH']}\")\n", "print(f\"💾 Output: {CONFIG['OUTPUT_DIRECTORY']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Import Libraries and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import logging\n", "from typing import Dict, List, Optional\n", "from pathlib import Path\n", "import tempfile\n", "from datetime import datetime\n", "\n", "# Google Cloud and PDF processing imports\n", "from google.cloud import storage\n", "import PyPDF2\n", "import pdfplumber\n", "import pandas as pd\n", "from openpyxl import Workbook\n", "from openpyxl.styles import <PERSON>ont, PatternFill, Alignment\n", "import fitz  # PyMuPDF\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Create output directory\n", "os.makedirs(CONFIG['OUTPUT_DIRECTORY'], exist_ok=True)\n", "\n", "print(\"✅ Libraries imported and setup complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. PDF Data Extractor Class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PDFDataExtractor:\n", "    \"\"\"Extract specific data from PDF documents in Google Cloud Storage.\"\"\"\n", "    \n", "    def __init__(self):\n", "        \"\"\"Initialize the PDF extractor.\"\"\"\n", "        self.bucket_name = CONFIG['GCS_BUCKET_NAME']\n", "        self.folder_path = CONFIG['GCS_FOLDER_PATH']\n", "        self.storage_client = storage.Client()\n", "        self.bucket = self.storage_client.bucket(self.bucket_name)\n", "        self.patterns = CONFIG['EXTRACTION_PATTERNS']\n", "        \n", "        print(f\"🔗 Connected to bucket: {self.bucket_name}\")\n", "    \n", "    def list_pdf_files(self) -> List[str]:\n", "        \"\"\"List all PDF files in the specified GCS folder.\"\"\"\n", "        try:\n", "            blobs = self.bucket.list_blobs(prefix=self.folder_path)\n", "            pdf_files = [blob.name for blob in blobs if blob.name.lower().endswith('.pdf')]\n", "            logger.info(f\"Found {len(pdf_files)} PDF files in {self.folder_path}\")\n", "            return pdf_files\n", "        except Exception as e:\n", "            logger.error(f\"Error listing PDF files: {e}\")\n", "            return []\n", "    \n", "    def download_pdf(self, blob_name: str) -> Optional[str]:\n", "        \"\"\"Download PDF from GCS to temporary file.\"\"\"\n", "        try:\n", "            blob = self.bucket.blob(blob_name)\n", "            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')\n", "            blob.download_to_filename(temp_file.name)\n", "            logger.info(f\"Downloaded {blob_name} to {temp_file.name}\")\n", "            return temp_file.name\n", "        except Exception as e:\n", "            logger.error(f\"Error downloading {blob_name}: {e}\")\n", "            return None\n", "    \n", "    def extract_text_from_pdf(self, pdf_path: str) -> str:\n", "        \"\"\"Extract text from PDF using multiple methods for better accuracy.\"\"\"\n", "        text_content = \"\"\n", "        \n", "        # Method 1: PyMuPDF (fitz) - often better for complex layouts\n", "        try:\n", "            doc = fitz.open(pdf_path)\n", "            for page_num in range(len(doc)):\n", "                page = doc.load_page(page_num)\n", "                text_content += page.get_text() + \"\\n\"\n", "            doc.close()\n", "            logger.info(f\"Extracted text using PyMuPDF: {len(text_content)} characters\")\n", "        except Exception as e:\n", "            logger.warning(f\"PyMuPDF extraction failed: {e}\")\n", "        \n", "        # Method 2: pdfplumber - good for tables and structured data\n", "        if not text_content.strip():\n", "            try:\n", "                with pdfplumber.open(pdf_path) as pdf:\n", "                    for page in pdf.pages:\n", "                        page_text = page.extract_text()\n", "                        if page_text:\n", "                            text_content += page_text + \"\\n\"\n", "                logger.info(f\"Extracted text using pdfplumber: {len(text_content)} characters\")\n", "            except Exception as e:\n", "                logger.warning(f\"pdfplumber extraction failed: {e}\")\n", "        \n", "        # Method 3: PyPDF2 - fallback option\n", "        if not text_content.strip():\n", "            try:\n", "                with open(pdf_path, 'rb') as file:\n", "                    pdf_reader = PyPDF2.PdfReader(file)\n", "                    for page in pdf_reader.pages:\n", "                        text_content += page.extract_text() + \"\\n\"\n", "                logger.info(f\"Extracted text using PyPDF2: {len(text_content)} characters\")\n", "            except Exception as e:\n", "                logger.error(f\"PyPDF2 extraction failed: {e}\")\n", "        \n", "        return text_content\n", "\n", "print(\"✅ PDFDataExtractor class defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Extraction and Excel Creation Methods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Continue PDFDataExtractor class with additional methods\n", "def extract_information(self, text: str) -> Dict[str, str]:\n", "    \"\"\"Extract specific information using regex patterns.\"\"\"\n", "    extracted_data = {}\n", "    \n", "    # Clean text for better pattern matching\n", "    clean_text = re.sub(r'\\s+', ' ', text.lower())\n", "    \n", "    for category, patterns in self.patterns.items():\n", "        extracted_data[category] = \"\"\n", "        \n", "        for pattern in patterns:\n", "            matches = re.finditer(pattern, clean_text, re.IGNORECASE | re.MULTILINE)\n", "            for match in matches:\n", "                if match.group(1).strip():\n", "                    extracted_data[category] = match.group(1).strip()\n", "                    break\n", "            \n", "            if extracted_data[category]:\n", "                break\n", "        \n", "        # Clean up extracted data\n", "        if extracted_data[category]:\n", "            extracted_data[category] = re.sub(r'\\s+', ' ', extracted_data[category])\n", "            extracted_data[category] = extracted_data[category][:CONFIG['EXCEL_STYLING']['max_text_length']]\n", "    \n", "    return extracted_data\n", "\n", "def create_excel_file(self, pdf_name: str, extracted_data: Dict[str, str]) -> str:\n", "    \"\"\"Create an Excel file with the extracted data.\"\"\"\n", "    # Create output filename\n", "    base_name = Path(pdf_name).stem\n", "    excel_filename = os.path.join(CONFIG['OUTPUT_DIRECTORY'], f\"{base_name}{CONFIG['EXCEL_FILE_SUFFIX']}\")\n", "    \n", "    # Create workbook and worksheet\n", "    wb = Workbook()\n", "    ws = wb.active\n", "    ws.title = \"Extracted Data\"\n", "    \n", "    # Set up headers\n", "    headers = [\"Category\", \"Extracted Information\"]\n", "    ws.append(headers)\n", "    \n", "    # Style headers\n", "    header_font = Font(bold=True, color=CONFIG['EXCEL_STYLING']['header_font_color'])\n", "    header_fill = PatternFill(\n", "        start_color=CONFIG['EXCEL_STYLING']['header_background_color'], \n", "        end_color=CONFIG['EXCEL_STYLING']['header_background_color'], \n", "        fill_type=\"solid\"\n", "    )\n", "    \n", "    for col in range(1, len(headers) + 1):\n", "        cell = ws.cell(row=1, column=col)\n", "        cell.font = header_font\n", "        cell.fill = header_fill\n", "        cell.alignment = Alignment(horizontal=\"center\")\n", "    \n", "    # Add data rows\n", "    categories = {\n", "        'seasonal_sizes': 'Seasonal Sourcing Sizes',\n", "        'fiber_position': 'Fiber Text Position',\n", "        'care_symbols': 'Care Symbols',\n", "        'care_instructions': 'Care Instructions'\n", "    }\n", "    \n", "    for key, display_name in categories.items():\n", "        value = extracted_data.get(key, \"Not found\")\n", "        ws.append([display_name, value])\n", "    \n", "    # Adjust column widths\n", "    ws.column_dimensions['A'].width = CONFIG['EXCEL_STYLING']['column_width_category']\n", "    ws.column_dimensions['B'].width = CONFIG['EXCEL_STYLING']['column_width_data']\n", "    \n", "    # Save the file\n", "    wb.save(excel_filename)\n", "    logger.info(f\"Created Excel file: {excel_filename}\")\n", "    return excel_filename\n", "\n", "# Add methods to the class\n", "PDFDataExtractor.extract_information = extract_information\n", "PDFDataExtractor.create_excel_file = create_excel_file\n", "\n", "print(\"✅ Extraction and Excel creation methods added!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Processing Methods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Continue PDFDataExtractor class with processing methods\n", "def process_single_pdf(self, blob_name: str) -> Optional[str]:\n", "    \"\"\"Process a single PDF file and create Excel output.\"\"\"\n", "    logger.info(f\"Processing PDF: {blob_name}\")\n", "    \n", "    # Download PDF\n", "    temp_pdf_path = self.download_pdf(blob_name)\n", "    if not temp_pdf_path:\n", "        return None\n", "    \n", "    try:\n", "        # Extract text\n", "        text_content = self.extract_text_from_pdf(temp_pdf_path)\n", "        if not text_content.strip():\n", "            logger.warning(f\"No text extracted from {blob_name}\")\n", "            return None\n", "        \n", "        # Extract specific information\n", "        extracted_data = self.extract_information(text_content)\n", "        \n", "        # Create Excel file\n", "        excel_filename = self.create_excel_file(blob_name, extracted_data)\n", "        \n", "        return excel_filename\n", "        \n", "    finally:\n", "        # Clean up temporary file\n", "        if os.path.exists(temp_pdf_path):\n", "            os.unlink(temp_pdf_path)\n", "\n", "def process_all_pdfs(self) -> List[str]:\n", "    \"\"\"Process all PDFs in the specified folder.\"\"\"\n", "    pdf_files = self.list_pdf_files()\n", "    if not pdf_files:\n", "        logger.warning(\"No PDF files found to process\")\n", "        return []\n", "    \n", "    excel_files = []\n", "    for pdf_file in pdf_files:\n", "        try:\n", "            excel_file = self.process_single_pdf(pdf_file)\n", "            if excel_file:\n", "                excel_files.append(excel_file)\n", "                logger.info(f\"Successfully processed: {pdf_file}\")\n", "            else:\n", "                logger.error(f\"Failed to process: {pdf_file}\")\n", "        except Exception as e:\n", "            logger.error(f\"Error processing {pdf_file}: {e}\")\n", "    \n", "    logger.info(f\"Processing complete. Created {len(excel_files)} Excel files.\")\n", "    return excel_files\n", "\n", "# Add methods to the class\n", "PDFDataExtractor.process_single_pdf = process_single_pdf\n", "PDFDataExtractor.process_all_pdfs = process_all_pdfs\n", "\n", "print(\"✅ Processing methods added!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Authentication Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check Google Cloud authentication\n", "try:\n", "    from google.auth import default\n", "    credentials, project = default()\n", "    print(f\"✅ Google Cloud authentication successful!\")\n", "    print(f\"📋 Project: {project}\")\n", "except Exception as e:\n", "    print(f\"❌ Authentication failed: {e}\")\n", "    print(\"\\n🔧 To fix authentication, run one of these commands in a terminal:\")\n", "    print(\"   gcloud auth application-default login\")\n", "    print(\"   OR set GOOGLE_APPLICATION_CREDENTIALS environment variable\")\n", "    print(\"\\n💡 In Google Colab/JupyterLab, you might need to:\")\n", "    print(\"   1. Upload your service account key file\")\n", "    print(\"   2. Set the environment variable to point to it\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Test Connection and List PDFs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize extractor and test connection\n", "try:\n", "    extractor = PDFDataExtractor()\n", "    \n", "    # List available PDF files\n", "    pdf_files = extractor.list_pdf_files()\n", "    \n", "    print(f\"\\n📊 Found {len(pdf_files)} PDF files:\")\n", "    for i, pdf_file in enumerate(pdf_files[:10], 1):  # Show first 10 files\n", "        print(f\"   {i}. {pdf_file}\")\n", "    \n", "    if len(pdf_files) > 10:\n", "        print(f\"   ... and {len(pdf_files) - 10} more files\")\n", "    \n", "    if pdf_files:\n", "        print(\"\\n✅ Ready to process PDFs!\")\n", "    else:\n", "        print(\"\\n⚠️  No PDF files found. Please check:\")\n", "        print(f\"   - Bucket name: {CONFIG['GCS_BUCKET_NAME']}\")\n", "        print(f\"   - Folder path: {CONFIG['GCS_FOLDER_PATH']}\")\n", "        print(\"   - Your access permissions\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error connecting to Google Cloud Storage: {e}\")\n", "    print(\"\\n🔧 Please check:\")\n", "    print(\"   1. Your authentication is set up correctly\")\n", "    print(\"   2. You have access to the specified bucket\")\n", "    print(\"   3. The bucket and folder path are correct\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Process All PDFs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process all PDFs and create Excel files\n", "print(\"🚀 Starting PDF processing...\")\n", "print(f\"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"-\" * 50)\n", "\n", "try:\n", "    # Process all PDFs\n", "    excel_files = extractor.process_all_pdfs()\n", "    \n", "    print(\"-\" * 50)\n", "    print(f\"✅ Processing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "    \n", "    if excel_files:\n", "        print(f\"\\n🎉 Successfully created {len(excel_files)} Excel files:\")\n", "        for i, excel_file in enumerate(excel_files, 1):\n", "            print(f\"   {i}. {excel_file}\")\n", "        \n", "        print(f\"\\n📁 All files saved in: {CONFIG['OUTPUT_DIRECTORY']}/\")\n", "        \n", "        # Show file sizes\n", "        print(\"\\n📊 File sizes:\")\n", "        for excel_file in excel_files:\n", "            if os.path.exists(excel_file):\n", "                size_kb = os.path.getsize(excel_file) / 1024\n", "                print(f\"   {os.path.basename(excel_file)}: {size_kb:.1f} KB\")\n", "    else:\n", "        print(\"\\n⚠️  No Excel files were created.\")\n", "        print(\"   Check the logs above for specific errors.\")\n", "        \n", "except Exception as e:\n", "    print(f\"\\n❌ Error during processing: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON> Sample Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display sample results from the first Excel file\n", "try:\n", "    excel_files = [f for f in os.listdir(CONFIG['OUTPUT_DIRECTORY']) if f.endswith('.xlsx')]\n", "    \n", "    if excel_files:\n", "        sample_file = os.path.join(CONFIG['OUTPUT_DIRECTORY'], excel_files[0])\n", "        \n", "        print(f\"📋 Sample results from: {excel_files[0]}\")\n", "        print(\"-\" * 50)\n", "        \n", "        # Read and display the Excel file\n", "        df = pd.read_excel(sample_file)\n", "        \n", "        for index, row in df.iterrows():\n", "            category = row['Category']\n", "            info = row['Extracted Information']\n", "            print(f\"\\n🏷️  {category}:\")\n", "            print(f\"   {info}\")\n", "        \n", "        print(\"\\n\" + \"-\" * 50)\n", "        print(f\"📊 Total Excel files created: {len(excel_files)}\")\n", "        \n", "    else:\n", "        print(\"📭 No Excel files found in the output directory.\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error reading sample results: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Download Results (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a zip file with all Excel results for easy download\n", "import zipfile\n", "from datetime import datetime\n", "\n", "try:\n", "    excel_files = [f for f in os.listdir(CONFIG['OUTPUT_DIRECTORY']) if f.endswith('.xlsx')]\n", "    \n", "    if excel_files:\n", "        # Create zip filename with timestamp\n", "        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "        zip_filename = f\"adidas_pdf_extraction_results_{timestamp}.zip\"\n", "        \n", "        with zipfile.ZipFile(zip_filename, 'w') as zipf:\n", "            for excel_file in excel_files:\n", "                file_path = os.path.join(CONFIG['OUTPUT_DIRECTORY'], excel_file)\n", "                zipf.write(file_path, excel_file)\n", "        \n", "        zip_size_mb = os.path.getsize(zip_filename) / (1024 * 1024)\n", "        \n", "        print(f\"📦 Created zip file: {zip_filename}\")\n", "        print(f\"📏 Size: {zip_size_mb:.2f} MB\")\n", "        print(f\"📁 Contains {len(excel_files)} Excel files\")\n", "        print(\"\\n💾 You can download this zip file from the file browser.\")\n", "        \n", "    else:\n", "        print(\"📭 No Excel files to zip.\")\n", "        \n", "except Exception as e:\n", "    print(f\"❌ Error creating zip file: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 Summary\n", "\n", "This notebook has:\n", "\n", "1. ✅ **Installed** all required dependencies\n", "2. ✅ **Connected** to Google Cloud Storage\n", "3. ✅ **Listed** PDF files in `gs://dice_gstorage/adidas/`\n", "4. ✅ **Extracted** the following data from each PDF:\n", "   - Seasonal sourcing sizes\n", "   - Fiber Text Position\n", "   - Care symbols\n", "   - Care Instructions\n", "5. ✅ **Created** individual Excel files for each PDF\n", "6. ✅ **Organized** results in the `output_excel_files/` directory\n", "7. ✅ **Generated** a zip file for easy download\n", "\n", "### 📁 Output Structure:\n", "```\n", "output_excel_files/\n", "├── document1_extracted_data.xlsx\n", "├── document2_extracted_data.xlsx\n", "└── ...\n", "```\n", "\n", "### 🔧 Customization:\n", "- Modify the `CONFIG` dictionary in cell 2 to adjust extraction patterns\n", "- Change bucket/folder paths as needed\n", "- Customize Excel styling and output format\n", "\n", "### 📞 Support:\n", "If you encounter issues:\n", "1. Check Google Cloud authentication\n", "2. Verify bucket access permissions\n", "3. Review the extraction patterns for your PDF format\n", "4. Check the logs for specific error messages"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}