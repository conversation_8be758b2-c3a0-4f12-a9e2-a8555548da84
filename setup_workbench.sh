#!/bin/bash

# Setup script for Google Cloud Workbench
# This script installs required dependencies and sets up the environment

echo "Setting up PDF Extractor for Google Cloud Workbench..."

# Update system packages
echo "Updating system packages..."
sudo apt-get update

# Install system dependencies for PDF processing
echo "Installing system dependencies..."
sudo apt-get install -y \
    python3-dev \
    python3-pip \
    libpoppler-cpp-dev \
    pkg-config \
    python3-poppler

# Upgrade pip
echo "Upgrading pip..."
python3 -m pip install --upgrade pip

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install -r requirements.txt

# Set up Google Cloud authentication (if not already done)
echo "Checking Google Cloud authentication..."
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "No active Google Cloud authentication found."
    echo "Please run: gcloud auth application-default login"
    echo "Or set up service account authentication."
else
    echo "Google Cloud authentication is active."
fi

# Create output directory
mkdir -p output_excel_files

echo "Setup complete!"
echo ""
echo "To run the PDF extractor:"
echo "  python3 pdf_extractor.py"
echo ""
echo "Make sure you have:"
echo "1. Google Cloud authentication set up"
echo "2. Access to the gs://dice_gstorage bucket"
echo "3. PDF files in the gs://dice_gstorage/adidas/ folder"
