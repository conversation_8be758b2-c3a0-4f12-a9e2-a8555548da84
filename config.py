"""
Configuration file for PDF Data Extractor
Modify these settings according to your requirements
"""

# Google Cloud Storage Configuration
GCS_BUCKET_NAME = "dice_gstorage"
GCS_FOLDER_PATH = "adidas/"

# Output Configuration
OUTPUT_DIRECTORY = "output_excel_files"
EXCEL_FILE_SUFFIX = "_extracted_data.xlsx"

# Extraction Patterns Configuration
# You can modify these patterns to better match your PDF content
EXTRACTION_PATTERNS = {
    'seasonal_sizes': [
        r'seasonal\s+sourcing\s+sizes?[:\s]*([^\n]+)',
        r'sizes?\s*[:\-]\s*([XS|S|M|L|XL|XXL|\d+\-\d+|\d+\/\d+][,\s]*)+',
        r'size\s+range[:\s]*([^\n]+)',
        r'available\s+sizes?[:\s]*([^\n]+)'
    ],
    'fiber_position': [
        r'fiber\s+text\s+position[:\s]*([^\n]+)',
        r'fiber\s+position[:\s]*([^\n]+)',
        r'text\s+position[:\s]*([^\n]+)',
        r'label\s+position[:\s]*([^\n]+)'
    ],
    'care_symbols': [
        r'care\s+symbols?[:\s]*([^\n]+)',
        r'washing\s+symbols?[:\s]*([^\n]+)',
        r'laundry\s+symbols?[:\s]*([^\n]+)',
        r'maintenance\s+symbols?[:\s]*([^\n]+)'
    ],
    'care_instructions': [
        r'care\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
        r'washing\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
        r'laundry\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
        r'maintenance\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)'
    ]
}

# Excel Styling Configuration
EXCEL_STYLING = {
    'header_font_color': "FFFFFF",
    'header_background_color': "366092",
    'column_width_category': 25,
    'column_width_data': 50,
    'max_text_length': 200
}

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# Processing Configuration
MAX_RETRIES = 3
TEMP_FILE_CLEANUP = True
