# PDF Data Extractor for Adidas Documents

This Python script extracts specific information from PDF documents stored in Google Cloud Storage and creates Excel files with the extracted data.

## Extracted Information

The script extracts the following details from each PDF:
- **Seasonal sourcing sizes**
- **Fiber Text Position**
- **Care symbols**
- **Care Instructions**

## Prerequisites

1. **Google Cloud Workbench** environment
2. **Google Cloud Storage access** to `gs://dice_gstorage/adidas/`
3. **Python 3.7+** (usually pre-installed in Workbench)
4. **Google Cloud authentication** set up

## Setup Instructions

### 1. Clone or Upload Files

Upload the following files to your Google Cloud Workbench:
- `pdf_extractor.py` - Main extraction script
- `config.py` - Configuration settings
- `requirements.txt` - Python dependencies
- `setup_workbench.sh` - Setup script
- `README.md` - This documentation

### 2. Run Setup Script

```bash
chmod +x setup_workbench.sh
./setup_workbench.sh
```

This will:
- Install system dependencies
- Install Python packages
- Create output directory
- Check Google Cloud authentication

### 3. Configure Google Cloud Authentication

If not already set up, authenticate with Google Cloud:

```bash
# For user authentication
gcloud auth application-default login

# Or for service account (if you have a service account key)
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

## Usage

### Basic Usage

Run the script to process all PDFs in the configured location:

```bash
python3 pdf_extractor.py
```

### Configuration

Modify `config.py` to customize:

- **GCS_BUCKET_NAME**: Change the bucket name (default: "dice_gstorage")
- **GCS_FOLDER_PATH**: Change the folder path (default: "adidas/")
- **OUTPUT_DIRECTORY**: Change output directory (default: "output_excel_files")
- **EXTRACTION_PATTERNS**: Modify regex patterns for better extraction

### Example Configuration Changes

```python
# In config.py
GCS_BUCKET_NAME = "your-bucket-name"
GCS_FOLDER_PATH = "your-folder-path/"
OUTPUT_DIRECTORY = "custom_output_folder"
```

## Output

For each PDF processed, the script creates an Excel file with:
- **Filename**: `{original_pdf_name}_extracted_data.xlsx`
- **Location**: `output_excel_files/` directory
- **Content**: Structured data with categories and extracted information

### Excel File Structure

| Category | Extracted Information |
|----------|----------------------|
| Seasonal Sourcing Sizes | [Extracted size information] |
| Fiber Text Position | [Extracted position information] |
| Care Symbols | [Extracted care symbols] |
| Care Instructions | [Extracted care instructions] |

## Troubleshooting

### Common Issues

1. **Authentication Error**
   ```
   Error: Could not automatically determine credentials
   ```
   **Solution**: Run `gcloud auth application-default login`

2. **Permission Denied**
   ```
   Error: 403 Forbidden
   ```
   **Solution**: Ensure your account has access to the GCS bucket

3. **No Text Extracted**
   ```
   Warning: No text extracted from [filename]
   ```
   **Solution**: PDF might be image-based. Consider OCR preprocessing

4. **Module Not Found**
   ```
   ModuleNotFoundError: No module named 'xyz'
   ```
   **Solution**: Run `pip3 install -r requirements.txt`

### Improving Extraction Accuracy

1. **Modify Regex Patterns**: Update patterns in `config.py` to match your PDF format
2. **Add New Patterns**: Include additional regex patterns for better matching
3. **Adjust Text Processing**: Modify the text cleaning logic in the script

### Example Pattern Customization

```python
# In config.py, modify EXTRACTION_PATTERNS
EXTRACTION_PATTERNS = {
    'seasonal_sizes': [
        r'seasonal\s+sourcing\s+sizes?[:\s]*([^\n]+)',
        r'your_custom_pattern_here',  # Add your pattern
    ],
    # ... other patterns
}
```

## Logging

The script provides detailed logging:
- **INFO**: General processing information
- **WARNING**: Non-critical issues
- **ERROR**: Processing failures

Logs are displayed in the console during execution.

## File Structure

```
project/
├── pdf_extractor.py      # Main script
├── config.py             # Configuration
├── requirements.txt      # Dependencies
├── setup_workbench.sh    # Setup script
├── README.md            # Documentation
└── output_excel_files/  # Output directory (created automatically)
    ├── document1_extracted_data.xlsx
    ├── document2_extracted_data.xlsx
    └── ...
```

## Advanced Usage

### Processing Specific PDFs

To process specific PDFs, modify the script or create a custom version:

```python
# Example: Process only specific files
extractor = PDFDataExtractor()
specific_files = ["adidas/document1.pdf", "adidas/document2.pdf"]
for pdf_file in specific_files:
    extractor.process_single_pdf(pdf_file)
```

### Batch Processing with Error Handling

The script includes retry logic and error handling for robust batch processing.

## Support

For issues or improvements:
1. Check the troubleshooting section
2. Review the logs for specific error messages
3. Modify configuration settings as needed
4. Consider updating regex patterns for your specific PDF format
