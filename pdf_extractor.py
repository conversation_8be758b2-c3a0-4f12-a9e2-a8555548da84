#!/usr/bin/env python3
"""
PDF Data Extractor for Adidas Documents
Extracts specific information from PDFs stored in Google Cloud Storage
and saves the results to Excel files.

Required information to extract:
- Seasonal sourcing sizes
- Fiber Text Position
- Care symbols
- Care Instructions
"""

import os
import re
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import tempfile

# Google Cloud and PDF processing imports
from google.cloud import storage
import PyPDF2
import pdfplumber
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import fitz  # PyMuPDF for better text extraction

# Import configuration
from config import (
    GCS_BUCKET_NAME, GCS_FOLDER_PATH, OUTPUT_DIRECTORY,
    EXCEL_FILE_SUFFIX, EXTRACTION_PATTERNS, EXCEL_STYLING,
    LOG_LEVEL, LOG_FORMAT, MAX_RETRIES, TEMP_FILE_CLEANUP
)

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)
logger = logging.getLogger(__name__)


class PDFDataExtractor:
    """Extract specific data from PDF documents."""
    
    def __init__(self, bucket_name: str = None, folder_path: str = None):
        """
        Initialize the PDF extractor.

        Args:
            bucket_name: Google Cloud Storage bucket name (defaults to config)
            folder_path: Path to the folder containing PDFs (defaults to config)
        """
        self.bucket_name = bucket_name or GCS_BUCKET_NAME
        self.folder_path = folder_path or GCS_FOLDER_PATH
        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(self.bucket_name)

        # Create output directory if it doesn't exist
        os.makedirs(OUTPUT_DIRECTORY, exist_ok=True)

        # Use patterns from configuration
        self.patterns = EXTRACTION_PATTERNS
    
    def list_pdf_files(self) -> List[str]:
        """List all PDF files in the specified GCS folder."""
        try:
            blobs = self.bucket.list_blobs(prefix=self.folder_path)
            pdf_files = [blob.name for blob in blobs if blob.name.lower().endswith('.pdf')]
            logger.info(f"Found {len(pdf_files)} PDF files in {self.folder_path}")
            return pdf_files
        except Exception as e:
            logger.error(f"Error listing PDF files: {e}")
            return []
    
    def download_pdf(self, blob_name: str) -> Optional[str]:
        """Download PDF from GCS to temporary file."""
        try:
            blob = self.bucket.blob(blob_name)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            blob.download_to_filename(temp_file.name)
            logger.info(f"Downloaded {blob_name} to {temp_file.name}")
            return temp_file.name
        except Exception as e:
            logger.error(f"Error downloading {blob_name}: {e}")
            return None
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF using multiple methods for better accuracy."""
        text_content = ""
        
        # Method 1: PyMuPDF (fitz) - often better for complex layouts
        try:
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text() + "\n"
            doc.close()
            logger.info(f"Extracted text using PyMuPDF: {len(text_content)} characters")
        except Exception as e:
            logger.warning(f"PyMuPDF extraction failed: {e}")
        
        # Method 2: pdfplumber - good for tables and structured data
        if not text_content.strip():
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text_content += page_text + "\n"
                logger.info(f"Extracted text using pdfplumber: {len(text_content)} characters")
            except Exception as e:
                logger.warning(f"pdfplumber extraction failed: {e}")
        
        # Method 3: PyPDF2 - fallback option
        if not text_content.strip():
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text_content += page.extract_text() + "\n"
                logger.info(f"Extracted text using PyPDF2: {len(text_content)} characters")
            except Exception as e:
                logger.error(f"PyPDF2 extraction failed: {e}")
        
        return text_content
    
    def extract_information(self, text: str) -> Dict[str, str]:
        """Extract specific information using regex patterns."""
        extracted_data = {}
        
        # Clean text for better pattern matching
        clean_text = re.sub(r'\s+', ' ', text.lower())
        
        for category, patterns in self.patterns.items():
            extracted_data[category] = ""
            
            for pattern in patterns:
                matches = re.finditer(pattern, clean_text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if match.group(1).strip():
                        extracted_data[category] = match.group(1).strip()
                        break
                
                if extracted_data[category]:
                    break
            
            # Clean up extracted data
            if extracted_data[category]:
                extracted_data[category] = re.sub(r'\s+', ' ', extracted_data[category])
                extracted_data[category] = extracted_data[category][:EXCEL_STYLING['max_text_length']]
        
        return extracted_data
    
    def create_excel_file(self, pdf_name: str, extracted_data: Dict[str, str]) -> str:
        """Create an Excel file with the extracted data."""
        # Create output filename
        base_name = Path(pdf_name).stem
        excel_filename = os.path.join(OUTPUT_DIRECTORY, f"{base_name}{EXCEL_FILE_SUFFIX}")
        
        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Extracted Data"
        
        # Set up headers
        headers = ["Category", "Extracted Information"]
        ws.append(headers)
        
        # Style headers
        header_font = Font(bold=True, color=EXCEL_STYLING['header_font_color'])
        header_fill = PatternFill(
            start_color=EXCEL_STYLING['header_background_color'],
            end_color=EXCEL_STYLING['header_background_color'],
            fill_type="solid"
        )
        
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Add data rows
        categories = {
            'seasonal_sizes': 'Seasonal Sourcing Sizes',
            'fiber_position': 'Fiber Text Position',
            'care_symbols': 'Care Symbols',
            'care_instructions': 'Care Instructions'
        }
        
        for key, display_name in categories.items():
            value = extracted_data.get(key, "Not found")
            ws.append([display_name, value])
        
        # Adjust column widths
        ws.column_dimensions['A'].width = EXCEL_STYLING['column_width_category']
        ws.column_dimensions['B'].width = EXCEL_STYLING['column_width_data']
        
        # Save the file
        wb.save(excel_filename)
        logger.info(f"Created Excel file: {excel_filename}")
        return excel_filename
    
    def process_single_pdf(self, blob_name: str) -> Optional[str]:
        """Process a single PDF file and create Excel output."""
        logger.info(f"Processing PDF: {blob_name}")
        
        # Download PDF
        temp_pdf_path = self.download_pdf(blob_name)
        if not temp_pdf_path:
            return None
        
        try:
            # Extract text
            text_content = self.extract_text_from_pdf(temp_pdf_path)
            if not text_content.strip():
                logger.warning(f"No text extracted from {blob_name}")
                return None
            
            # Extract specific information
            extracted_data = self.extract_information(text_content)
            
            # Create Excel file
            excel_filename = self.create_excel_file(blob_name, extracted_data)
            
            return excel_filename
            
        finally:
            # Clean up temporary file
            if TEMP_FILE_CLEANUP and os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)
    
    def process_all_pdfs(self) -> List[str]:
        """Process all PDFs in the specified folder."""
        pdf_files = self.list_pdf_files()
        if not pdf_files:
            logger.warning("No PDF files found to process")
            return []
        
        excel_files = []
        for pdf_file in pdf_files:
            try:
                excel_file = self.process_single_pdf(pdf_file)
                if excel_file:
                    excel_files.append(excel_file)
                    logger.info(f"Successfully processed: {pdf_file}")
                else:
                    logger.error(f"Failed to process: {pdf_file}")
            except Exception as e:
                logger.error(f"Error processing {pdf_file}: {e}")
        
        logger.info(f"Processing complete. Created {len(excel_files)} Excel files.")
        return excel_files


def main():
    """Main execution function."""
    try:
        # Initialize extractor
        extractor = PDFDataExtractor()
        
        # Process all PDFs
        excel_files = extractor.process_all_pdfs()
        
        if excel_files:
            print(f"\nSuccessfully created {len(excel_files)} Excel files:")
            for file in excel_files:
                print(f"  - {file}")
        else:
            print("No Excel files were created. Check the logs for errors.")
            
    except Exception as e:
        logger.error(f"Main execution error: {e}")
        raise


if __name__ == "__main__":
    main()
