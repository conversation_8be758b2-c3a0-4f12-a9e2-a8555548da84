!pip install google-cloud-storage PyPDF2 pdfplumber PyMuPDF pandas openpyxl --quiet
print("Dependencies installed successfully!")

___________________________________________________________________________________________________


# Configuration settings - Modify these as needed
CONFIG = {
    # Google Cloud Storage settings
    'GCS_BUCKET_NAME': 'dice_gstorage',
    'GCS_FOLDER_PATH': 'AdidasPdfs/',
    
    # Output settings
    'OUTPUT_DIRECTORY': 'output_excel_files',
    'EXCEL_FILE_SUFFIX': '_extracted_data.xlsx',
    
    # Extraction patterns for different data types
    'EXTRACTION_PATTERNS': {
        'seasonal_sizes': [
            r'seasonal\s+sourcing\s+sizes?[:\s]*([^\n]+)',
            r'sizes?\s*[:\-]\s*([XS|S|M|L|XL|XXL|\d+\-\d+|\d+\/\d+][,\s]*)+',
            r'size\s+range[:\s]*([^\n]+)',
            r'available\s+sizes?[:\s]*([^\n]+)'
        ],
        'fiber_position': [
            r'fiber\s+text\s+position[:\s]*([^\n]+)',
            r'fiber\s+position[:\s]*([^\n]+)',
            r'text\s+position[:\s]*([^\n]+)',
            r'label\s+position[:\s]*([^\n]+)'
        ],
        'care_symbols': [
            r'care\s+symbols?[:\s]*([^\n]+)',
            r'washing\s+symbols?[:\s]*([^\n]+)',
            r'laundry\s+symbols?[:\s]*([^\n]+)',
            r'maintenance\s+symbols?[:\s]*([^\n]+)'
        ],
        'care_instructions': [
            r'care\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
            r'washing\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
            r'laundry\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)',
            r'maintenance\s+instructions?[:\s]*([^\n\r]+(?:\n[^\n\r]+)*)'
        ]
    },
    
    # Excel styling
    'EXCEL_STYLING': {
        'header_font_color': 'FFFFFF',
        'header_background_color': '366092',
        'column_width_category': 25,
        'column_width_data': 50,
        'max_text_length': 200
    }
}

print(" Configuration loaded successfully!")
print(f"Bucket: {CONFIG['GCS_BUCKET_NAME']}")
print(f"Folder: {CONFIG['GCS_FOLDER_PATH']}")
print(f"Output: {CONFIG['OUTPUT_DIRECTORY']}")

___________________________________________________________________________________________________


import os
import re
import logging
from typing import Dict, List, Optional
from pathlib import Path
import tempfile
from datetime import datetime

# Google Cloud and PDF processing imports
from google.cloud import storage
import PyPDF2
import pdfplumber
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
import fitz  # PyMuPDF

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create output directory
os.makedirs(CONFIG['OUTPUT_DIRECTORY'], exist_ok=True)

print("Libraries imported and setup complete!")


___________________________________________________________________________________________________

--PDF Data Extractor Class


class PDFDataExtractor:
    """Extract specific data from PDF documents in Google Cloud Storage."""
    
    def __init__(self):
        """Initialize the PDF extractor."""
        self.bucket_name = CONFIG['GCS_BUCKET_NAME']
        self.folder_path = CONFIG['GCS_FOLDER_PATH']
        self.storage_client = storage.Client()
        self.bucket = self.storage_client.bucket(self.bucket_name)
        self.patterns = CONFIG['EXTRACTION_PATTERNS']
        
        print(f"Connected to bucket: {self.bucket_name}")
    
    def list_pdf_files(self) -> List[str]:
        """List all PDF files in the specified GCS folder."""
        try:
            blobs = self.bucket.list_blobs(prefix=self.folder_path)
            pdf_files = [blob.name for blob in blobs if blob.name.lower().endswith('.pdf')]
            logger.info(f"Found {len(pdf_files)} PDF files in {self.folder_path}")
            return pdf_files
        except Exception as e:
            logger.error(f"Error listing PDF files: {e}")
            return []
    
    def download_pdf(self, blob_name: str) -> Optional[str]:
        """Download PDF from GCS to temporary file."""
        try:
            blob = self.bucket.blob(blob_name)
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
            blob.download_to_filename(temp_file.name)
            logger.info(f"Downloaded {blob_name} to {temp_file.name}")
            return temp_file.name
        except Exception as e:
            logger.error(f"Error downloading {blob_name}: {e}")
            return None
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF using multiple methods for better accuracy."""
        text_content = ""
        
        # Method 1: PyMuPDF (fitz) - often better for complex layouts
        try:
            doc = fitz.open(pdf_path)
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text() + "\n"
            doc.close()
            logger.info(f"Extracted text using PyMuPDF: {len(text_content)} characters")
        except Exception as e:
            logger.warning(f"PyMuPDF extraction failed: {e}")
        
        # Method 2: pdfplumber - good for tables and structured data
        if not text_content.strip():
            try:
                with pdfplumber.open(pdf_path) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text_content += page_text + "\n"
                logger.info(f"Extracted text using pdfplumber: {len(text_content)} characters")
            except Exception as e:
                logger.warning(f"pdfplumber extraction failed: {e}")
        
        # Method 3: PyPDF2 - fallback option
        if not text_content.strip():
            try:
                with open(pdf_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text_content += page.extract_text() + "\n"
                logger.info(f"Extracted text using PyPDF2: {len(text_content)} characters")
            except Exception as e:
                logger.error(f"PyPDF2 extraction failed: {e}")
        
        return text_content

print(" PDFDataExtractor class defined!")

___________________________________________________________________________________________________

# Continue PDFDataExtractor class with processing methods
def process_single_pdf(self, blob_name: str) -> Optional[str]:
    """Process a single PDF file and create Excel output."""
    logger.info(f"Processing PDF: {blob_name}")
    
    # Download PDF
    temp_pdf_path = self.download_pdf(blob_name)
    if not temp_pdf_path:
        return None
    
    try:
        # Extract text
        text_content = self.extract_text_from_pdf(temp_pdf_path)
        if not text_content.strip():
            logger.warning(f"No text extracted from {blob_name}")
            return None
        
        # Extract specific information
        extracted_data = self.extract_information(text_content)
        
        # Create Excel file
        excel_filename = self.create_excel_file(blob_name, extracted_data)
        
        return excel_filename
        
    finally:
        # Clean up temporary file
        if os.path.exists(temp_pdf_path):
            os.unlink(temp_pdf_path)

def process_all_pdfs(self) -> List[str]:
    """Process all PDFs in the specified folder."""
    pdf_files = self.list_pdf_files()
    if not pdf_files:
        logger.warning("No PDF files found to process")
        return []
    
    excel_files = []
    for pdf_file in pdf_files:
        try:
            excel_file = self.process_single_pdf(pdf_file)
            if excel_file:
                excel_files.append(excel_file)
                logger.info(f"Successfully processed: {pdf_file}")
            else:
                logger.error(f"Failed to process: {pdf_file}")
        except Exception as e:
            logger.error(f"Error processing {pdf_file}: {e}")
    
    logger.info(f"Processing complete. Created {len(excel_files)} Excel files.")
    return excel_files

# Add methods to the class
PDFDataExtractor.process_single_pdf = process_single_pdf
PDFDataExtractor.process_all_pdfs = process_all_pdfs

print(" Processing methods added!")

_________________________________________________________________________________________________
# Process all PDFs and create Excel files
print(" Starting PDF processing...")
print(f" Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("-" * 50)

try:
    # Process all PDFs
    excel_files = extractor.process_all_pdfs()
    
    print("-" * 50)
    print(f" Processing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if excel_files:
        print(f"\n Successfully created {len(excel_files)} Excel files:")
        for i, excel_file in enumerate(excel_files, 1):
            print(f"   {i}. {excel_file}")
        
        print(f"\n All files saved in: {CONFIG['OUTPUT_DIRECTORY']}/")
        
        # Show file sizes
        print("\n File sizes:")
        for excel_file in excel_files:
            if os.path.exists(excel_file):
                size_kb = os.path.getsize(excel_file) / 1024
                print(f"   {os.path.basename(excel_file)}: {size_kb:.1f} KB")
    else:
        print("\n  No Excel files were created.")
        print("   Check the logs above for specific errors.")
        
except Exception as e:
    print(f"\n Error during processing: {e}")
    import traceback
    traceback.print_exc()

___________________________________________________________________